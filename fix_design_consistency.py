#!/usr/bin/env python3
"""
Script to fix design consistency issues across all HTML pages.
Removes emojis and ensures proper structure.
"""

import os
import re
from pathlib import Path

def remove_emojis(text):
    """Remove emojis from text"""
    # Emoji pattern
    emoji_pattern = re.compile("["
        u"\U0001F600-\U0001F64F"  # emoticons
        u"\U0001F300-\U0001F5FF"  # symbols & pictographs
        u"\U0001F680-\U0001F6FF"  # transport & map symbols
        u"\U0001F1E0-\U0001F1FF"  # flags (iOS)
        u"\U00002702-\U000027B0"
        u"\U000024C2-\U0001F251"
        u"\U0001F900-\U0001F9FF"  # supplemental symbols
        u"\U0001FA00-\U0001FAFF"
        "]+", flags=re.UNICODE)
    return emoji_pattern.sub(r'', text)

def fix_list_spacing(content):
    """Fix list spacing by removing <br /> tags between list items"""
    # Replace </li><br /> <li> with </li><li>
    content = re.sub(r'</li>\s*<br\s*/>\s*<li>', '</li><li>', content)
    return content

def fix_html_structure(filepath):
    """Fix HTML structure for consistency"""
    print(f"Processing: {filepath}")
    
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # Remove emojis
    content = remove_emojis(content)
    
    # Fix list spacing
    content = fix_list_spacing(content)
    
    # Fix search input placeholder if present
    content = content.replace('placeholder="🔍 Search', 'placeholder="Search')
    
    # Fix common emoji patterns in headings
    content = re.sub(r'<h4>[🎓🔬🧪🏗️📚📈🏆🚧✈️🛣️♻️🔬🛢️]\s*', '<h4>', content)
    
    # Fix old quickfindsLinks structure if it's not properly styled
    if '<div class="quickfindsLinks">' in content and 'style=' not in content.split('<div class="quickfindsLinks">')[1].split('</div>')[0]:
        # This is an old-style quickfinds - we could enhance it but let's keep it minimal for now
        pass
    
    # Only write if content changed
    if content != original_content:
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  ✓ Updated {filepath}")
        return True
    else:
        print(f"  - No changes needed for {filepath}")
        return False

def main():
    """Main function to process all HTML files"""
    base_dir = Path(__file__).parent
    
    # HTML files to process (excluding backups and templates)
    html_files = []
    for filepath in base_dir.glob('*.html'):
        filename = filepath.name
        if not any(x in filename for x in ['backup', 'old', 'template', 'TEMPLATE']):
            if filename not in ['header.html', 'footer.html']:
                html_files.append(filepath)
    
    print(f"Found {len(html_files)} HTML files to process\n")
    
    updated_count = 0
    for filepath in sorted(html_files):
        if fix_html_structure(filepath):
            updated_count += 1
    
    print(f"\n✓ Completed! Updated {updated_count} out of {len(html_files)} files")

if __name__ == "__main__":
    main()
