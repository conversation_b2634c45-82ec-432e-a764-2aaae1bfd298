<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="[PAGE DESCRIPTION]">
    <title>[PAGE TITLE] | Dr. Ra<PERSON>qul <PERSON></title>
    <link href="theme/styles.css" rel="stylesheet" type="text/css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function(){
            $("#headerDiv").load("header.html");
            $("#footerDiv").load("footer.html");
            $(".nav-link[href='[FILENAME].html']").addClass("active");
        });
    </script>
</head>

<body>
    <div id="mainDiv">
        <div id="headerDiv"></div>

        <!-- Page Hero Section -->
        <div class="hero-section-full" style="background-image: url('images/banner_image.jpg');">
            <div class="hero-content-full">
                <h1>[PAGE HEADING]</h1>
                <p>[PAGE SUBTITLE]</p>
            </div>
        </div>

        <div id="contentDiv">
            <div class="content-container">
                <!-- 
                    NO SIDEBAR - FULL WIDTH CONTENT ONLY
                    All subpages use clean, full-width layout without images
                -->
                
                <h2 class="section-title">Section Title</h2>
                
                <!-- For lists -->
                <ul class="ul_twoline">
                    <li>List item 1</li>
                    <li>List item 2</li>
                </ul>

                <!-- For cards grid -->
                <div class="info-cards-grid">
                    <div class="info-card">
                        <div class="card-header">
                            <h3>Card Title</h3>
                        </div>
                        <div class="card-content">
                            <p>Card content here</p>
                        </div>
                    </div>
                </div>

                <!-- For highlight boxes -->
                <div class="highlight-box">
                    <h3>Highlight Title</h3>
                    <p>Important information or callout content</p>
                </div>

                <!-- For tables -->
                <table>
                    <thead>
                        <tr>
                            <th>Column 1</th>
                            <th>Column 2</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Data 1</td>
                            <td>Data 2</td>
                        </tr>
                    </tbody>
                </table>

            </div>
        </div>

        <div id="footerDiv"></div>
    </div>
</body>
</html>
