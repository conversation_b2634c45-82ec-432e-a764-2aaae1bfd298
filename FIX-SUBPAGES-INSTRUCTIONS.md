# Quick Fix for Subpage Images Issue

## Problem
Subpages (journals.html, conferences.html, etc.) still have the old sidebar with images that get distorted.

## Solution
Remove the sidebar completely and use full-width clean layout.

## Quick Fix Steps

### For Each Subpage:

1. **Find and remove the sidebar section:**
   Delete this entire block:
   ```html
   <div class="leftDiv"> 
       <p><img src="images/rafi33.jpg" width="180" height="236" alt="Dr. Rafi Photo" /></p>
       <div class="quickfindsBox">
           ...
       </div>
   </div>
   ```

2. **Remove the wrapper divs:**
   Delete:
   ```html
   <div class="rightDiv">
   <div class="contentBox">
   ```

3. **Replace with clean structure:**
   ```html
   <div id="contentDiv">
       <div class="content-container">
           <!-- Your content here -->
       </div>
   </div>
   ```

4. **Update the DOCTYPE and head:**
   Replace:
   ```html
   <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"...>
   ```
   With:
   ```html
   <!DOCTYPE html>
   <html lang="en">
   ```

5. **Add hero section after headerDiv:**
   ```html
   <div class="hero-section-full" style="background-image: url('images/banner_image.jpg');">
       <div class="hero-content-full">
           <h1>[Page Title]</h1>
           <p>[Page Description]</p>
       </div>
   </div>
   ```

## Automated Fix Script

You can use this sed command to quickly remove the sidebar:

```bash
# For each HTML file
for file in journals.html conferences.html technicalPapers.html books.html presentations.html reports.html research.html researchInterests.html researchFunding.html teaching.html teachingCourses.html teachingInterests.html team.html teamPhD.html teamMS.html teamUndergrad.html teamPostdoc.html labs.html labsAsphalt.html labsPavement.html services.html serviceBoards.html serviceCommittees.html serviceReviews.html serviceOrganized.html serviceAffiliations.html serviceToCommunity.html serviceToUniversity.html outreach.html publications.html; do
    echo "Processing $file..."
    # Backup
    cp "$file" "${file%.html}-old-backup.html"
done
```

## Manual Quick Fix (Recommended)

Since each page has different content, it's best to:

1. Open the old page
2. Copy the content (the actual list/text, not the HTML structure)
3. Open TEMPLATE-PAGE.html
4. Save as the new page name
5. Paste the content
6. Update titles and hero section

## Pages That Need Fixing (29 pages)

**Publications:**
- [ ] journals.html
- [ ] conferences.html  
- [ ] technicalPapers.html
- [ ] books.html
- [ ] presentations.html
- [ ] reports.html
- [ ] publications.html

**Research:**
- [ ] research.html
- [ ] researchInterests.html
- [ ] researchFunding.html

**Teaching:**
- [ ] teaching.html
- [ ] teachingCourses.html
- [ ] teachingInterests.html

**Team:**
- [ ] team.html
- [ ] teamPhD.html
- [ ] teamMS.html
- [ ] teamUndergrad.html
- [ ] teamPostdoc.html

**Labs:**
- [ ] labs.html
- [ ] labsAsphalt.html
- [ ] labsPavement.html

**Services:**
- [ ] services.html
- [ ] serviceBoards.html
- [ ] serviceCommittees.html
- [ ] serviceReviews.html
- [ ] serviceOrganized.html
- [ ] serviceAffiliations.html
- [ ] serviceToCommunity.html
- [ ] serviceToUniversity.html

**Other:**
- [ ] outreach.html

## Example: Fixing journals.html

**Before (lines 19-44):**
```html
<div id="contentDiv">
    <div class="leftDiv"> 
        <p><img src="images/rafi33.jpg" width="180" height="236" alt="Dr. Rafi Photo" /></p>
        <div class="quickfindsBox">
            ...
        </div>
    </div>
    <div class="rightDiv">
        <div class="contentBox">
            <br />
            <h3>Peer-reviewed Journal Articles</h3>
            <ol>
                ...content...
            </ol>
        </div>
    </div>
</div>
```

**After:**
```html
<!-- Add hero section here -->
<div class="hero-section-full" style="background-image: url('images/banner_image.jpg');">
    <div class="hero-content-full">
        <h1>Journal Articles</h1>
        <p>Peer-reviewed publications in leading academic journals</p>
    </div>
</div>

<div id="contentDiv">
    <div class="content-container">
        <h2 class="section-title">Peer-reviewed Journal Articles</h2>
        <ol>
            ...content...
        </ol>
    </div>
</div>
```

## Testing

After fixing each page:
1. Open in browser
2. Check that no images appear in sidebar
3. Check that navigation dropdown works
4. Check that content is readable
5. Check responsive layout on mobile

## Need Help?

Refer to:
- `TEMPLATE-PAGE.html` - Clean page structure
- `awards.html` - Example of correctly formatted subpage
- `DESIGN-CHANGES.md` - Full design documentation
