#!/usr/bin/env python3
"""
<PERSON><PERSON>t to automatically fix all subpages by removing sidebar images
and converting to the new clean layout.
"""

import os
import re

# Page configurations: filename -> (title, description)
PAGE_CONFIG = {
    'journals.html': ('Journal Articles', 'Peer-reviewed publications in leading academic journals'),
    'conferences.html': ('Conference Papers', 'Presentations and proceedings from international conferences'),
    'technicalPapers.html': ('Technical Papers', 'Technical reports and white papers'),
    'books.html': ('Books & Chapters', 'Published books and book chapters'),
    'presentations.html': ('Presentations', 'Conference and seminar presentations'),
    'reports.html': ('Research Reports', 'Technical reports and research documents'),
    'researchInterests.html': ('Research Interests', 'Key areas of research focus and expertise'),
    'researchFunding.html': ('Research Funding', 'External grants and funding sources'),
    'teachingCourses.html': ('Courses Taught', 'Undergraduate and graduate courses'),
    'teachingInterests.html': ('Teaching Interests', 'Teaching philosophy and interests'),
    'team.html': ('Research Team', 'Meet our talented researchers and students'),
    'teamPhD.html': ('PhD Students', 'Doctoral research students'),
    'teamMS.html': ('MS Students', 'Master\'s research students'),
    'teamUndergrad.html': ('Undergraduate Students', 'Undergraduate research assistants'),
    'teamPostdoc.html': ('Postdoctoral Researchers', 'Postdoctoral fellows and researchers'),
    'labs.html': ('Laboratory Facilities', 'State-of-the-art research laboratories'),
    'labsAsphalt.html': ('Asphalt Laboratory', 'Advanced asphalt characterization facilities'),
    'labsPavement.html': ('Pavement Laboratory', 'Pavement materials testing facilities'),
    'services.html': ('Professional Services', 'Academic and professional service activities'),
    'serviceBoards.html': ('Editorial Boards', 'Journal editorial board memberships'),
    'serviceCommittees.html': ('Committees', 'Professional committee memberships'),
    'serviceReviews.html': ('Reviews', 'Journal and conference reviews'),
    'serviceOrganized.html': ('Events Organized', 'Conferences and workshops organized'),
    'serviceAffiliations.html': ('Affiliations', 'Professional society memberships'),
    'serviceToCommunity.html': ('Community Service', 'Service to the community'),
    'serviceToUniversity.html': ('University Service', 'Service to the university'),
    'outreach.html': ('Links & Resources', 'Useful links and resources'),
}

HTML_TEMPLATE = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="{description}">
    <title>{title} | Dr. Rafiqul A. Tarefder</title>
    <link href="theme/styles.css" rel="stylesheet" type="text/css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function(){{
            $("#headerDiv").load("header.html");
            $("#footerDiv").load("footer.html");
            $(".nav-link[href='{filename}']").addClass("active");
        }});
    </script>
</head>

<body>
    <div id="mainDiv">
        <div id="headerDiv"></div>

        <!-- Hero Section -->
        <div class="hero-section-full" style="background-image: url('images/banner_image.jpg');">
            <div class="hero-content-full">
                <h1>{title}</h1>
                <p>{description}</p>
            </div>
        </div>

        <div id="contentDiv">
            <div class="content-container">
{content}
            </div>
        </div>

        <div id="footerDiv"></div>
    </div>
</body>
</html>
"""

def extract_content(html):
    """Extract main content from old HTML structure."""
    # Try to find content between rightDiv/contentBox and closing tags
    patterns = [
        r'<div class="rightDiv">.*?<div class="contentBox">(.*?)</div>\s*</div>\s*</div>\s*<div id="footerDiv"',
        r'<div class="rightDiv">(.*?)</div>\s*</div>\s*<div id="footerDiv"',
        r'<div class="contentBox">(.*?)</div>\s*</div>\s*</div>\s*<div id="footerDiv"',
    ]
    
    for pattern in patterns:
        match = re.search(pattern, html, re.DOTALL)
        if match:
            return match.group(1).strip()
    
    # Fallback: extract everything between contentDiv and footerDiv
    match = re.search(r'<div id="contentDiv">(.*?)<div id="footerDiv"', html, re.DOTALL)
    if match:
        content = match.group(1)
        # Remove leftDiv and quickfindsBox if present
        content = re.sub(r'<div class="leftDiv">.*?</div>', '', content, flags=re.DOTALL)
        return content.strip()
    
    return ""

def process_file(filename, base_dir):
    """Process a single HTML file."""
    filepath = os.path.join(base_dir, filename)
    
    if not os.path.exists(filepath):
        print(f"⚠️  File not found: {filename}")
        return False
    
    if filename not in PAGE_CONFIG:
        print(f"⚠️  No configuration for: {filename}")
        return False
    
    title, description = PAGE_CONFIG[filename]
    
    # Read original file
    with open(filepath, 'r', encoding='utf-8') as f:
        old_content = f.read()
    
    # Extract main content
    content = extract_content(old_content)
    
    if not content:
        print(f"❌ Could not extract content from: {filename}")
        return False
    
    # Generate new HTML
    new_html = HTML_TEMPLATE.format(
        title=title,
        description=description,
        filename=filename,
        content=content
    )
    
    # Write new file
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(new_html)
    
    print(f"✅ Fixed: {filename}")
    return True

def main():
    base_dir = os.path.dirname(os.path.abspath(__file__))
    
    print("=" * 60)
    print("Fixing all subpages - Removing sidebar images")
    print("=" * 60)
    print()
    
    success_count = 0
    fail_count = 0
    
    for filename in sorted(PAGE_CONFIG.keys()):
        if process_file(filename, base_dir):
            success_count += 1
        else:
            fail_count += 1
    
    print()
    print("=" * 60)
    print(f"✅ Successfully fixed: {success_count} pages")
    print(f"❌ Failed: {fail_count} pages")
    print("=" * 60)
    print()
    print("All pages now have:")
    print("  - No sidebar images")
    print("  - Full-width hero sections")
    print("  - Clean, consistent layout")
    print()

if __name__ == '__main__':
    main()
