# Simple Fix for Image Distortion in Subpages

## The Problem
When you click on dropdown menu items (like "Journal Articles" under Publications), the old pages still have a sidebar with an image that gets distorted with the new CSS.

## The Simple Solution

### Step 1: Open the old page and find the content

Open any subpage (e.g., `journals.html`) and locate this section:
```html
<div class="leftDiv"> <!-- DELETE THIS ENTIRE SECTION -->
    <p><img src="images/rafi33.jpg" ... /></p>
    <div class="quickfindsBox">...</div>
</div>

<div class="rightDiv">  <!-- KEEP CONTENT, DELETE WRAPPER -->
    <div class="contentBox">  <!-- DELETE THIS TOO -->
        <!-- THIS IS THE CONTENT YOU WANT TO KEEP -->
        <h3>Peer-reviewed Journal Articles</h3>
        <ol>
            <li>Article 1...</li>
            <li>Article 2...</li>
        </ol>
    </div>
</div>
```

### Step 2: Use This New Structure

Replace the entire `<body>` section with this clean structure:

```html
<body>
    <div id="mainDiv">
        <div id="headerDiv"></div>

        <!-- Hero Section (customize title and description) -->
        <div class="hero-section-full" style="background-image: url('images/banner_image.jpg');">
            <div class="hero-content-full">
                <h1>Journal Articles</h1>
                <p>Peer-reviewed publications in leading journals</p>
            </div>
        </div>

        <!-- Content Area (NO SIDEBAR, NO IMAGES) -->
        <div id="contentDiv">
            <div class="content-container">
                <!-- PASTE YOUR CONTENT HERE -->
                <h2 class="section-title">Peer-reviewed Journal Articles</h2>
                <ol>
                    <li>Your content...</li>
                </ol>
            </div>
        </div>

        <div id="footerDiv"></div>
    </div>
</body>
```

## Quick Reference: What to Change for Each Page

| Page | Hero Title | Hero Description |
|------|-----------|------------------|
| journals.html | Journal Articles | Peer-reviewed publications in leading academic journals |
| conferences.html | Conference Papers | Presentations and proceedings from international conferences |
| technicalPapers.html | Technical Papers | Technical reports and white papers |
| books.html | Books & Chapters | Published books and book chapters |
| presentations.html | Presentations | Conference and seminar presentations |
| reports.html | Research Reports | Technical reports and research documents |
| research.html | Research | Cutting-edge research in civil engineering |
| teaching.html | Teaching | Courses and educational contributions |
| team.html | Research Team | Meet our talented researchers and students |
| labs.html | Laboratory Facilities | State-of-the-art research laboratories |
| services.html | Professional Services | Academic and professional service activities |

## Visual Guide

### BEFORE (with distorted image):
```
┌─────────────────────────────────────┐
│ Header + Navigation                 │
├──────────┬──────────────────────────┤
│  [IMG]   │  Content                 │
│  Photo   │  - List item 1           │
│          │  - List item 2           │
│ Sidebar  │  - List item 3           │
└──────────┴──────────────────────────┘
```

### AFTER (clean, no images):
```
┌─────────────────────────────────────┐
│ Header + Navigation                 │
├─────────────────────────────────────┤
│     Full Width Hero Image           │
│         Page Title                  │
├─────────────────────────────────────┤
│  Full Width Content                 │
│  - List item 1                      │
│  - List item 2                      │
│  - List item 3                      │
└─────────────────────────────────────┘
```

## Quick Copy-Paste Template

Save this as a file and use it for all subpages:

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="[PAGE DESCRIPTION]">
    <title>[PAGE TITLE] | Dr. Rafiqul A. Tarefder</title>
    <link href="theme/styles.css" rel="stylesheet" type="text/css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function(){
            $("#headerDiv").load("header.html");
            $("#footerDiv").load("footer.html");
            $(".nav-link[href='[FILENAME].html']").addClass("active");
        });
    </script>
</head>

<body>
    <div id="mainDiv">
        <div id="headerDiv"></div>

        <div class="hero-section-full" style="background-image: url('images/banner_image.jpg');">
            <div class="hero-content-full">
                <h1>[HERO TITLE]</h1>
                <p>[HERO DESCRIPTION]</p>
            </div>
        </div>

        <div id="contentDiv">
            <div class="content-container">
                <!-- YOUR CONTENT GOES HERE -->
                <!-- NO SIDEBAR - NO IMAGES - JUST CONTENT -->
            </div>
        </div>

        <div id="footerDiv"></div>
    </div>
</body>
</html>
```

## Do This for EVERY Subpage

1. Open old page
2. Copy only the content (articles list, text, etc.)
3. Open template above
4. Paste content in the marked area
5. Update [PAGE TITLE], [HERO TITLE], [HERO DESCRIPTION], [FILENAME]
6. Save and test

## Result

✅ No more distorted images
✅ Clean, professional layout  
✅ Full-width content
✅ Consistent design across all pages
✅ Better mobile responsiveness
