@charset "utf-8";

/* ============================================
   APANM-Inspired Professional Academic Design
   ============================================ */

/* Modern CSS Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
    overflow-x: hidden;
}

/* Add smooth transitions globally */
* {
    transition: background-color 0.3s ease, color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: #333333;
    background-color: #f5f5f5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Main Container */
#mainDiv {
    max-width: 100%;
    margin: 0;
    background-color: #ffffff;
    min-height: 100vh;
}

/* ==================
   TOP BAR STYLES - APANM INSPIRED
   ================== */

.top-bar {
    background: #ffffff;
    border-bottom: 2px solid #e5e5e5;
    padding: 1.25rem 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.top-bar-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    transition: transform 0.3s ease;
}

.logo-section:hover {
    transform: translateY(-2px);
}

.logo-section img {
    height: 80px;
    width: auto;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
}

.site-title h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #8B2332;
    margin-bottom: 0.25rem;
    line-height: 1.2;
    transition: color 0.3s ease;
}

.site-title h1:hover {
    color: #a82d3f;
}

.site-title p {
    font-size: 0.95rem;
    color: #666;
    font-weight: 400;
}

.contact-info {
    display: flex;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.contact-item:hover {
    background: #f8f8f8;
    transform: translateY(-2px);
}

.contact-item .icon {
    font-size: 1.5rem;
    transition: transform 0.3s ease;
}

.contact-item:hover .icon {
    transform: scale(1.1);
}

.contact-item small {
    display: block;
    font-size: 0.75rem;
    color: #666;
    font-weight: 500;
}

.contact-item .phone {
    display: block;
    font-size: 1.1rem;
    font-weight: 600;
    color: #8B2332;
    text-decoration: none;
    transition: color 0.3s ease;
}

.contact-item .phone:hover {
    color: #a82d3f;
}

/* ==================
   NAVIGATION STYLES - APANM INSPIRED
   ================== */

.main-nav {
    background: #8B2332;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    position: relative;
    z-index: 100;
}

.navMenu {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    list-style: none;
    display: flex;
    justify-content: center;
    align-items: stretch;
    gap: 0;
}

.navMenu > li {
    position: relative;
    display: flex;
    align-items: stretch;
}

.navMenu > li > a {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem 1.75rem;
    color: #ffffff;
    text-decoration: none;
    font-weight: 500;
    font-size: 1rem;
    transition: all 0.3s ease;
    white-space: nowrap;
    position: relative;
}

/* Enhanced hover effect */
.navMenu > li > a:hover,
.navMenu > li > a.active {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Dropdown arrow styling */
.dropdown-arrow {
    font-size: 0.7rem;
    transition: transform 0.3s ease;
    margin-left: 0.25rem;
}

.dropdown:hover .dropdown-arrow {
    transform: rotate(180deg);
}

/* Enhanced Dropdown Menu Styles */
.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 240px;
    background: #ffffff;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-15px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
    border-radius: 0 0 6px 6px;
    overflow: hidden;
}

/* Show dropdown on hover with smooth animation */
.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
}

.dropdown-menu li:last-child {
    border-bottom: none;
}

.dropdown-menu li a {
    display: block;
    padding: 0.875rem 1.5rem;
    color: #333;
    text-decoration: none;
    font-size: 0.95rem;
    font-weight: 400;
    transition: all 0.3s ease;
    position: relative;
}

/* Enhanced hover effect for dropdown items */
.dropdown-menu li a:hover {
    background: linear-gradient(to right, #f8f8f8 0%, #ffffff 100%);
    color: #8B2332;
    padding-left: 2rem;
}

/* Add a subtle left border on hover */
.dropdown-menu li a:hover::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: #8B2332;
}

/* ==================
   CONTENT AREA
   ================== */

#contentDiv {
    background: #f5f5f5;
}

/* Full-Width Hero Section */
.hero-section-full {
    position: relative;
    min-height: 500px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    color: #ffffff;
    overflow: hidden;
}

.hero-section-full::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1;
}

.hero-content-full {
    position: relative;
    z-index: 2;
    max-width: 1000px;
    padding: 2rem;
}

.hero-content-full h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.hero-content-full p {
    font-size: 1.25rem;
    line-height: 1.8;
    margin-bottom: 2rem;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.hero-btn {
    display: inline-block;
    padding: 0.875rem 2rem;
    background: #C17640;
    color: #ffffff;
    text-decoration: none;
    font-weight: 600;
    border-radius: 4px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.hero-btn:hover {
    background: #a56334;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

.hero-btn-secondary {
    background: rgba(255,255,255,0.2);
    backdrop-filter: blur(10px);
}

.hero-btn-secondary:hover {
    background: rgba(255,255,255,0.3);
}

/* Content Container */
.content-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 3rem 2rem;
}

/* Two Column Layout */
.content-wrapper {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: 2rem;
    align-items: start;
}

/* Sidebar */
.leftDiv {
    background: #ffffff;
    border-radius: 4px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    position: sticky;
    top: 2rem;
}

.leftDiv img {
    width: 100%;
    border-radius: 4px;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Quick Navigation Sidebar */
.quickfindsBox {
    background: #ffffff;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    margin-top: 1.5rem;
    overflow: hidden;
}

.quickfindsHeader {
    background: #8B2332;
    color: white;
    padding: 1rem;
    font-weight: 600;
    text-align: center;
    font-size: 0.95rem;
}

.quickfindsLinks ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.quickfindsLinks li {
    border-bottom: 1px solid #f0f0f0;
}

.quickfindsLinks li:last-child {
    border-bottom: none;
}

.quickfindsLinks a {
    display: block;
    padding: 0.75rem 1rem;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.quickfindsLinks a:hover {
    background: #f8f8f8;
    color: #8B2332;
    padding-left: 1.25rem;
}

/* Main Content Area */
.rightDiv {
    background: #ffffff;
    border-radius: 4px;
    padding: 2.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

/* Section Titles */
.section-title {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 3px solid #8B2332;
}

.section-title-center {
    text-align: center;
    border-bottom: none;
    margin-bottom: 3rem;
    position: relative;
}

.section-title-center::after {
    content: '';
    display: block;
    width: 60px;
    height: 3px;
    background: #8B2332;
    margin: 1rem auto 0;
}

/* Stats Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: #8B2332;
    color: white;
    padding: 2rem;
    border-radius: 4px;
    text-align: center;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(139, 35, 50, 0.3);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    display: block;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1rem;
    opacity: 0.95;
}

/* Info Cards */
.info-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.info-card {
    background: #ffffff;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border-left: 4px solid #8B2332;
}

.info-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.card-header {
    padding: 1.5rem;
    background: #f8f8f8;
    border-bottom: 1px solid #e5e5e5;
}

.card-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #333;
}

.card-content {
    padding: 1.5rem;
}

.card-item {
    padding: 1rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.card-item:last-child {
    border-bottom: none;
}

.card-item-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.25rem;
}

.card-item-subtitle {
    color: #666;
    font-size: 0.95rem;
}

/* Featured Content Grid */
.featured-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.featured-card {
    background: #ffffff;
    border-radius: 4px;
    padding: 2rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
    border-left: 4px solid #8B2332;
}

.featured-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
    border-left-width: 6px;
}

.featured-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
}

.featured-card p {
    color: #666;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.featured-stats {
    font-size: 0.9rem;
    color: #8B2332;
    font-weight: 600;
}

/* Content Sections */
.content-section {
    margin-bottom: 3rem;
}

.content-section:last-child {
    margin-bottom: 0;
}

.content-section p {
    margin-bottom: 1rem;
    line-height: 1.8;
}

.content-section ul, .content-section ol {
    margin-left: 1.5rem;
    margin-bottom: 1rem;
}

.content-section li {
    margin-bottom: 0.5rem;
    line-height: 1.6;
}

/* Highlight Boxes */
.highlight-box {
    background: #f8f8f8;
    border-left: 4px solid #C17640;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border-radius: 4px;
}

.highlight-box h3 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

/* Tables */
table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 2rem;
}

th, td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #e5e5e5;
}

th {
    background: #f8f8f8;
    font-weight: 600;
    color: #333;
}

tr:hover {
    background: #fafafa;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background: #8B2332;
    color: #ffffff;
    text-decoration: none;
    font-weight: 600;
    border-radius: 4px;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn:hover {
    background: #a82d3f;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(139, 35, 50, 0.3);
}

.btn-secondary {
    background: #C17640;
}

.btn-secondary:hover {
    background: #a56334;
}

/* ==================
   FOOTER STYLES
   ================== */

#footerDiv {
    background: #2c2c2c;
    color: #ffffff;
    padding: 2rem 0 1rem;
    margin-top: 3rem;
}

.footer-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    font-size: 1.1rem;
    margin-bottom: 1rem;
    color: #ffffff;
}

.footer-section p, .footer-section li {
    font-size: 0.9rem;
    line-height: 1.8;
    color: #cccccc;
}

.footer-section ul {
    list-style: none;
    padding: 0;
}

.footer-section a {
    color: #cccccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #ffffff;
}

.footer-bottom {
    text-align: center;
    padding: 1.5rem 2rem;
    border-top: 1px solid #444;
    max-width: 1400px;
    margin: 0 auto;
}

.footer-bottom p {
    font-size: 0.85rem;
    color: #999;
}

/* ==================
   RESPONSIVE DESIGN
   ================== */

@media (max-width: 1024px) {
    .content-wrapper {
        grid-template-columns: 1fr;
    }
    
    .leftDiv {
        position: static;
    }
    
    .navMenu {
        flex-wrap: wrap;
    }
    
    .navMenu > li > a {
        padding: 0.875rem 1rem;
        font-size: 0.95rem;
    }
}

@media (max-width: 768px) {
    .top-bar-container {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }
    
    .logo-section {
        flex-direction: column;
        text-align: center;
    }
    
    .site-title h1 {
        font-size: 1.25rem;
    }
    
    .navMenu {
        flex-direction: column;
        padding: 0;
    }
    
    .navMenu > li {
        width: 100%;
    }
    
    .navMenu > li > a {
        padding: 1rem;
        justify-content: center;
    }
    
    .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        display: none;
    }
    
    .dropdown:hover .dropdown-menu {
        display: block;
    }
    
    .hero-content-full h1 {
        font-size: 2rem;
    }
    
    .hero-content-full p {
        font-size: 1rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .info-cards-grid, .featured-grid {
        grid-template-columns: 1fr;
    }
}

/* ==================
   ACHIEVEMENTS & HIGHLIGHTS
   ================== */

.key-achievements {
    margin-bottom: 1.5rem;
}

.achievement-row {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.875rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.achievement-row:last-child {
    border-bottom: none;
}

.achievement-metric {
    font-size: 1.5rem;
    font-weight: 700;
    color: #8B2332;
    min-width: 80px;
}

.achievement-desc {
    color: #666;
    font-size: 0.95rem;
}

.achievements-list {
    margin-top: 1rem;
}

.achievement-item {
    padding: 0.875rem 0;
    padding-left: 1.5rem;
    position: relative;
    line-height: 1.6;
    color: #555;
}

.achievement-item::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #8B2332;
    font-weight: bold;
}

.achievement-list .achievement-item {
    margin-bottom: 1.5rem;
    padding: 1.5rem;
    background: #f8f8f8;
    border-radius: 4px;
    border-left: 4px solid #8B2332;
}

.achievement-list .achievement-item h4 {
    color: #333;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.achievement-list .achievement-item p {
    margin-bottom: 0;
}

/* ==================
   PUBLICATIONS
   ================== */

.publications-list {
    margin-top: 2rem;
}

.publication-item {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    background: #fafafa;
    border-left: 4px solid #8B2332;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.publication-item:hover {
    background: #f5f5f5;
    transform: translateX(5px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.publication-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.publication-authors {
    font-size: 0.95rem;
    color: #666;
    margin-bottom: 0.25rem;
}

.publication-journal {
    font-size: 0.9rem;
    color: #888;
    font-style: italic;
}

.search-container {
    margin: 2rem 0;
}

#searchInput {
    width: 100%;
    padding: 1rem 1.5rem;
    font-size: 1rem;
    border: 2px solid #e5e5e5;
    border-radius: 4px;
    transition: all 0.3s ease;
}

#searchInput:focus {
    outline: none;
    border-color: #8B2332;
    box-shadow: 0 0 0 3px rgba(139, 35, 50, 0.1);
}

/* ==================
   RESEARCH CARDS
   ================== */

.research-stats, .teaching-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.stat-item {
    background: #8B2332;
    color: white;
    padding: 2rem;
    border-radius: 4px;
    text-align: center;
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(139, 35, 50, 0.3);
}

.stat-item .stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    display: block;
    margin-bottom: 0.5rem;
}

.stat-item .stat-label {
    font-size: 1rem;
    opacity: 0.95;
}

.research-areas {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.research-card {
    background: #ffffff;
    padding: 2rem;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border-left: 4px solid #8B2332;
}

.research-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.research-card h4 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.research-card ul {
    list-style: none;
    padding: 0;
}

.research-card li {
    padding: 0.5rem 0;
    padding-left: 1.5rem;
    position: relative;
    color: #555;
    line-height: 1.6;
}

.research-card li::before {
    content: '•';
    position: absolute;
    left: 0.5rem;
    color: #8B2332;
    font-weight: bold;
    font-size: 1.2rem;
}

.impact-highlights {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.impact-item {
    background: #f8f8f8;
    padding: 1.5rem;
    border-radius: 4px;
    border-left: 4px solid #C17640;
}

.impact-item h4 {
    color: #333;
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
}

.impact-item p {
    color: #666;
    margin-bottom: 0;
    line-height: 1.6;
}

.lab-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.lab-item {
    background: #f8f8f8;
    padding: 1.5rem;
    border-radius: 4px;
    border-left: 4px solid #8B2332;
}

.lab-item h4 {
    color: #333;
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
}

.lab-item p {
    color: #666;
    margin-bottom: 0;
    line-height: 1.6;
}

/* ==================
   TEACHING SECTIONS
   ================== */

.philosophy-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.philosophy-item {
    background: #f8f8f8;
    padding: 1.5rem;
    border-radius: 4px;
    border-left: 4px solid #8B2332;
}

.philosophy-item h4 {
    color: #333;
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
}

.philosophy-item p {
    color: #666;
    margin-bottom: 0;
    line-height: 1.6;
}

.course-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.course-card {
    background: #ffffff;
    padding: 2rem;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border-left: 4px solid #C17640;
}

.course-card.graduate {
    border-left-color: #8B2332;
}

.course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.course-code {
    display: inline-block;
    background: #8B2332;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-weight: 600;
    margin-bottom: 1rem;
}

.course-card.graduate .course-code {
    background: #C17640;
}

.course-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.75rem;
}

.course-description {
    color: #666;
    line-height: 1.6;
}

.diversity-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.diversity-item {
    background: #f8f8f8;
    padding: 2rem;
    border-radius: 4px;
    text-align: center;
    border: 2px solid #e5e5e5;
}

.diversity-number {
    font-size: 3rem;
    font-weight: 700;
    color: #8B2332;
    display: block;
    margin-bottom: 0.5rem;
}

.diversity-label {
    font-size: 0.95rem;
    color: #666;
    line-height: 1.6;
}

/* ==================
   CONTENT BOX
   ================== */

.contentBox {
    background: #ffffff;
    padding: 0;
    margin-bottom: 3rem;
}

.contentBox h3 {
    font-size: 1.75rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid #e5e5e5;
}

.contentBox > p {
    margin-bottom: 1.5rem;
    line-height: 1.8;
    color: #555;
}

.contentBox ul {
    list-style: none;
    padding: 0;
}

.contentBox ul li {
    padding: 0.5rem 0;
    padding-left: 1.5rem;
    position: relative;
    line-height: 1.6;
}

.contentBox ul li::before {
    content: '•';
    position: absolute;
    left: 0.5rem;
    color: #8B2332;
    font-weight: bold;
    font-size: 1.2rem;
}

/* Two-line spacing for lists */
.ul_twoline li {
    margin-bottom: 1rem;
}

.ul_oneline li {
    margin-bottom: 0.5rem;
}

/* Ordered lists in content */
.content-container ol {
    padding-left: 2rem;
    line-height: 2;
}

.content-container ol li {
    margin-bottom: 1rem;
    color: #555;
}

.content-container ul {
    list-style: none;
    padding-left: 0;
}

.content-container ul li {
    padding: 0.5rem 0;
    padding-left: 1.5rem;
    position: relative;
    line-height: 1.8;
    color: #555;
}

.content-container ul li::before {
    content: '•';
    position: absolute;
    left: 0.5rem;
    color: #8B2332;
    font-weight: bold;
    font-size: 1.2rem;
}

/* Exception for navigation ul */
.quickfindsLinks ul li::before {
    content: none;
}

.quickfindsLinks ul li {
    padding-left: 0;
}

/* Improved link styling */
.content-container a {
    color: #8B2332;
    text-decoration: none;
    transition: color 0.3s ease;
    border-bottom: 1px solid transparent;
}

.content-container a:hover {
    color: #a82d3f;
    border-bottom-color: #a82d3f;
}

/* Email style links */
.emailStyle {
    color: #8B2332;
    text-decoration: none;
}

.emailStyle:hover {
    text-decoration: underline;
}

/* Improved images in content */
.content-container img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin: 1rem 0;
}

/* Better spacing for paragraphs */
.content-container p {
    margin-bottom: 1rem;
    line-height: 1.8;
    color: #555;
}

.content-container strong {
    color: #333;
    font-weight: 600;
}

/* ==================
   UTILITY CLASSES
   ================== */

.text-center {
    text-align: center;
}

.mt-1 { margin-top: 1rem; }
.mt-2 { margin-top: 2rem; }
.mt-3 { margin-top: 3rem; }

.mb-1 { margin-bottom: 1rem; }
.mb-2 { margin-bottom: 2rem; }
.mb-3 { margin-bottom: 3rem; }

.p-1 { padding: 1rem; }
.p-2 { padding: 2rem; }
.p-3 { padding: 3rem; }
